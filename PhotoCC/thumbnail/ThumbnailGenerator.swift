//
//  ThumbnailGenerator.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//
import AppKit
// MARK: - ThumbnailGenerator
class ThumbnailGenerator: @unchecked Sendable {
    static let shared = ThumbnailGenerator()
    private let thumbnailSize: CGSize = CGSize(width: 200, height: 200)
    private var cache: [URL: NSImage] = [:]
    private let cacheQueue = DispatchQueue(label: "thumbnail.cache", attributes: .concurrent)
    private let generationQueue = DispatchQueue(label: "thumbnail.generation", qos: .userInitiated)

    private init() {}

    func generateThumbnail(for url: URL) -> NSImage? {
        ThumbnailPerformanceMonitor.shared.startLoad(for: url)

        // 检查缓存
        let result = cacheQueue.sync { () -> NSImage? in
            if let cachedThumbnail = cache[url] {
                ThumbnailPerformanceMonitor.shared.endLoad(for: url, fromCache: true)
                return cachedThumbnail
            }

            guard let originalImage = NSImage(contentsOf: url) else {
                ThumbnailPerformanceMonitor.shared.endLoad(for: url, fromCache: false)
                // 返回一个默认的占位图片而不是 nil
                let placeholderImage = NSImage(systemSymbolName: "photo", accessibilityDescription: nil) ?? NSImage()
                return placeholderImage
            }

            let thumbnail = createSquareThumbnail(from: originalImage, size: thumbnailSize)

            // 缓存缩略图
            if let thumbnail = thumbnail {
                cacheQueue.async(flags: .barrier) {
                    self.cache[url] = thumbnail
                }
            }

            ThumbnailPerformanceMonitor.shared.endLoad(for: url, fromCache: false)
            return thumbnail
        }

        return result
    }

    func generateThumbnailAsync(for url: URL) async -> NSImage? {
        return await withCheckedContinuation { continuation in
            generationQueue.async {
                let thumbnail = self.generateThumbnail(for: url)
                continuation.resume(returning: thumbnail)
            }
        }
    }

    private func createSquareThumbnail(from image: NSImage, size: CGSize) -> NSImage? {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else {
            return nil
        }

        let originalWidth = CGFloat(cgImage.width)
        let originalHeight = CGFloat(cgImage.height)

        // 计算正方形裁剪区域（居中取样）
        let cropSize = min(originalWidth, originalHeight)
        let cropX = (originalWidth - cropSize) / 2
        let cropY = (originalHeight - cropSize) / 2

        let cropRect = CGRect(x: cropX, y: cropY, width: cropSize, height: cropSize)

        guard let croppedCGImage = cgImage.cropping(to: cropRect) else {
            return nil
        }

        // 创建缩略图
        let thumbnailImage = NSImage(size: size)
        thumbnailImage.lockFocus()

        let context = NSGraphicsContext.current?.cgContext
        context?.interpolationQuality = .high

        let drawRect = CGRect(origin: .zero, size: size)
        context?.draw(croppedCGImage, in: drawRect)

        thumbnailImage.unlockFocus()

        return thumbnailImage
    }

    func clearCache() {
        cacheQueue.async(flags: .barrier) {
            self.cache.removeAll()
        }
    }

    func removeCachedThumbnail(for url: URL) {
        cacheQueue.async(flags: .barrier) {
            self.cache.removeValue(forKey: url)
        }
    }

    func getCacheSize() -> Int {
        return cacheQueue.sync {
            return cache.count
        }
    }
}

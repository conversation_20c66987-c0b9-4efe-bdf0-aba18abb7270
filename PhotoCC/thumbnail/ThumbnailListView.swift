//
//  ThumbnailListView.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

protocol ThumbnailListViewDelegate: AnyObject {
    func thumbnailListView(_ listView: ThumbnailListView, didSelectPhoto photo: PhotoItem)
}

class ThumbnailListView: NSView {
    
    // MARK: - Properties
    weak var delegate: ThumbnailListViewDelegate?
    
    private var scrollView: NSScrollView!
    private var collectionView: NSCollectionView!
    private var photos: [PhotoItem] = []
    private var selectedPhotoIndex: Int = -1
    
    // MARK: - Initialization
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        
        // 创建集合视图
        setupCollectionView()
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupCollectionView() {
        // 创建集合视图
        collectionView = NSCollectionView()
        collectionView.backgroundColors = [NSColor.clear]
        collectionView.isSelectable = true
        collectionView.allowsMultipleSelection = false
        
        // 设置布局
        let layout = NSCollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.itemSize = NSSize(width: 100, height: 100)
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 8
        layout.sectionInset = NSEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)
        collectionView.collectionViewLayout = layout
        
        // 注册单元格
        collectionView.register(
            ThumbnailCollectionViewItem.self,
            forItemWithIdentifier: NSUserInterfaceItemIdentifier("ThumbnailItem")
        )
        
        // 设置数据源和代理
        collectionView.dataSource = self
        collectionView.delegate = self
        
        // 创建滚动视图
        scrollView = NSScrollView()
        scrollView.documentView = collectionView
        scrollView.hasVerticalScroller = false
        scrollView.hasHorizontalScroller = true
        scrollView.autohidesScrollers = true
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        
        addSubview(scrollView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }
    
    // MARK: - Public Methods
    
    func setPhotos(_ photos: [PhotoItem]) {
        self.photos = photos
        collectionView.reloadData()
    }
    
    func selectPhoto(at index: Int) {
        guard index >= 0 && index < photos.count else { return }
        
        selectedPhotoIndex = index
        let indexPath = IndexPath(item: index, section: 0)
        collectionView.selectItems(at: [indexPath], scrollPosition: .centeredHorizontally)
    }
    
    func selectPhoto(_ photo: PhotoItem) {
        if let index = photos.firstIndex(where: { $0.id == photo.id }) {
            selectPhoto(at: index)
        }
    }
}

// MARK: - NSCollectionViewDataSource

extension ThumbnailListView: NSCollectionViewDataSource {
    
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        return photos.count
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        let item = collectionView.makeItem(
            withIdentifier: NSUserInterfaceItemIdentifier("ThumbnailItem"),
            for: indexPath
        ) as! ThumbnailCollectionViewItem
        
        let photo = photos[indexPath.item]
        item.configure(with: photo)
        
        return item
    }
}

// MARK: - NSCollectionViewDelegate

extension ThumbnailListView: NSCollectionViewDelegate {
    
    func collectionView(_ collectionView: NSCollectionView, didSelectItemsAt indexPaths: Set<IndexPath>) {
        guard let indexPath = indexPaths.first else { return }
        
        selectedPhotoIndex = indexPath.item
        let photo = photos[indexPath.item]
        delegate?.thumbnailListView(self, didSelectPhoto: photo)
    }
}

//
//  PreviewControlPanel.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

protocol PreviewControlPanelDelegate: AnyObject {
    func previewControlPanel(_ panel: PreviewControlPanel, didChangeZoom zoom: CGFloat)
    func previewControlPanel(_ panel: PreviewControlPanel, didRotateBy angle: CGFloat)
    func previewControlPanelDidResetView(_ panel: PreviewControlPanel)
}

class PreviewControlPanel: NSView {
    
    // MARK: - Properties
    weak var delegate: PreviewControlPanelDelegate?
    
    private var backgroundView: NSVisualEffectView!
    private var zoomSlider: NSSlider!
    private var zoomLabel: NSTextField!
    private var rotateLeftButton: NSButton!
    private var rotateRightButton: NSButton!
    private var resetButton: NSButton!
    
    private var currentZoom: CGFloat = 1.0
    
    // MARK: - Initialization
    
    override init(frame frameRect: NSR<PERSON>t) {
        super.init(frame: frameRect)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        wantsLayer = true
        
        // 创建背景视图
        setupBackgroundView()
        
        // 创建控件
        setupControls()
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupBackgroundView() {
        backgroundView = NSVisualEffectView()
        backgroundView.material = .hudWindow
        backgroundView.blendingMode = .behindWindow
        backgroundView.state = .active
        backgroundView.wantsLayer = true
        backgroundView.layer?.cornerRadius = 8
        backgroundView.translatesAutoresizingMaskIntoConstraints = false
        
        addSubview(backgroundView)
    }
    
    private func setupControls() {
        // 缩放滑块
        zoomSlider = NSSlider()
        zoomSlider.minValue = 0.1
        zoomSlider.maxValue = 5.0
        zoomSlider.doubleValue = 1.0
        zoomSlider.target = self
        zoomSlider.action = #selector(zoomSliderChanged(_:))
        zoomSlider.translatesAutoresizingMaskIntoConstraints = false
        
        // 缩放标签
        zoomLabel = NSTextField(labelWithString: "100%")
        zoomLabel.font = NSFont.systemFont(ofSize: 12)
        zoomLabel.textColor = NSColor.labelColor
        zoomLabel.alignment = .center
        zoomLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 左旋转按钮
        rotateLeftButton = NSButton()
        rotateLeftButton.image = NSImage(systemSymbolName: "rotate.left", accessibilityDescription: "向左旋转")
        rotateLeftButton.bezelStyle = .regularSquare
        rotateLeftButton.isBordered = false
        rotateLeftButton.target = self
        rotateLeftButton.action = #selector(rotateLeftButtonClicked)
        rotateLeftButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 右旋转按钮
        rotateRightButton = NSButton()
        rotateRightButton.image = NSImage(systemSymbolName: "rotate.right", accessibilityDescription: "向右旋转")
        rotateRightButton.bezelStyle = .regularSquare
        rotateRightButton.isBordered = false
        rotateRightButton.target = self
        rotateRightButton.action = #selector(rotateRightButtonClicked)
        rotateRightButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 重置按钮
        resetButton = NSButton()
        resetButton.image = NSImage(systemSymbolName: "arrow.counterclockwise", accessibilityDescription: "重置视图")
        resetButton.bezelStyle = .regularSquare
        resetButton.isBordered = false
        resetButton.target = self
        resetButton.action = #selector(resetButtonClicked)
        resetButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加到背景视图
        backgroundView.addSubview(zoomSlider)
        backgroundView.addSubview(zoomLabel)
        backgroundView.addSubview(rotateLeftButton)
        backgroundView.addSubview(rotateRightButton)
        backgroundView.addSubview(resetButton)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 背景视图约束
            backgroundView.topAnchor.constraint(equalTo: topAnchor),
            backgroundView.leadingAnchor.constraint(equalTo: leadingAnchor),
            backgroundView.trailingAnchor.constraint(equalTo: trailingAnchor),
            backgroundView.bottomAnchor.constraint(equalTo: bottomAnchor),
            
            // 缩放滑块约束
            zoomSlider.leadingAnchor.constraint(equalTo: backgroundView.leadingAnchor, constant: 16),
            zoomSlider.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            zoomSlider.widthAnchor.constraint(equalToConstant: 120),
            
            // 缩放标签约束
            zoomLabel.leadingAnchor.constraint(equalTo: zoomSlider.trailingAnchor, constant: 8),
            zoomLabel.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            zoomLabel.widthAnchor.constraint(equalToConstant: 50),
            
            // 左旋转按钮约束
            rotateLeftButton.leadingAnchor.constraint(equalTo: zoomLabel.trailingAnchor, constant: 16),
            rotateLeftButton.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            rotateLeftButton.widthAnchor.constraint(equalToConstant: 32),
            rotateLeftButton.heightAnchor.constraint(equalToConstant: 32),
            
            // 右旋转按钮约束
            rotateRightButton.leadingAnchor.constraint(equalTo: rotateLeftButton.trailingAnchor, constant: 8),
            rotateRightButton.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            rotateRightButton.widthAnchor.constraint(equalToConstant: 32),
            rotateRightButton.heightAnchor.constraint(equalToConstant: 32),
            
            // 重置按钮约束
            resetButton.leadingAnchor.constraint(equalTo: rotateRightButton.trailingAnchor, constant: 16),
            resetButton.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            resetButton.trailingAnchor.constraint(equalTo: backgroundView.trailingAnchor, constant: -16),
            resetButton.widthAnchor.constraint(equalToConstant: 32),
            resetButton.heightAnchor.constraint(equalToConstant: 32)
        ])
    }
    
    // MARK: - Actions
    
    @objc private func zoomSliderChanged(_ sender: NSSlider) {
        currentZoom = CGFloat(sender.doubleValue)
        updateZoomLabel()
        delegate?.previewControlPanel(self, didChangeZoom: currentZoom)
    }
    
    @objc private func rotateLeftButtonClicked() {
        delegate?.previewControlPanel(self, didRotateBy: -90)
    }
    
    @objc private func rotateRightButtonClicked() {
        delegate?.previewControlPanel(self, didRotateBy: 90)
    }
    
    @objc private func resetButtonClicked() {
        currentZoom = 1.0
        zoomSlider.doubleValue = 1.0
        updateZoomLabel()
        delegate?.previewControlPanelDidResetView(self)
    }
    
    // MARK: - Public Methods
    
    func setZoom(_ zoom: CGFloat) {
        currentZoom = zoom
        zoomSlider.doubleValue = Double(zoom)
        updateZoomLabel()
    }
    
    // MARK: - Private Methods
    
    private func updateZoomLabel() {
        let percentage = Int(currentZoom * 100)
        zoomLabel.stringValue = "\(percentage)%"
    }
}

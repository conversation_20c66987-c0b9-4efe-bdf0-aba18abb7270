//
//  ImagePreviewView.swift
//  PhotoCC
//
//  Created by t<PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

protocol ImagePreviewViewDelegate: AnyObject {
    func imagePreviewView(_ previewView: ImagePreviewView, didChangeZoom zoom: CGFloat)
    func imagePreviewView(_ previewView: ImagePreviewView, didChangeRotation rotation: CGFloat)
}

class ImagePreviewView: NSView {

    // MARK: - Properties
    weak var delegate: ImagePreviewViewDelegate?

    private var scrollView: NSScrollView!
    private var imageView: NSImageView!
    private var gestureView: GestureHandlerView!

    // State properties
    private var currentImage: NSImage?
    private var currentZoom: CGFloat = 1.0
    private var currentRotation: CGFloat = 0.0
    private var currentOffset: NSPoint = NSPoint.zero
    private var isDragging: Bool = false
    private var isLoading: Bool = false

    // Constants
    private let minScale: CGFloat = 0.1
    private let maxScale: CGFloat = 50.0
    private let dragSpeed: CGFloat = 1.0
    
    // MARK: - Initialization

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    // MARK: - UI Setup

    private func setupUI() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.black.cgColor

        // 创建图片视图
        imageView = NSImageView()
        imageView.imageScaling = .scaleProportionallyUpOrDown
        imageView.imageAlignment = .alignCenter
        imageView.wantsLayer = true

        // 创建滚动视图
        scrollView = NSScrollView()
        scrollView.documentView = imageView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = true
        scrollView.autohidesScrollers = true
        scrollView.allowsMagnification = true
        scrollView.minMagnification = minScale
        scrollView.maxMagnification = maxScale
        scrollView.magnification = 1.0
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.backgroundColor = NSColor.black

        // 创建手势处理视图
        gestureView = GestureHandlerView(frame: NSRect.zero)
        gestureView.translatesAutoresizingMaskIntoConstraints = false
        gestureView.wantsLayer = true
        gestureView.layer?.backgroundColor = NSColor.clear.cgColor

        // 设置手势回调
        gestureView.onDragChanged = { [weak self] translation in
            self?.handleDragChanged(translation: translation)
        }

        gestureView.onDragEnded = { [weak self] translation in
            self?.handleDragEnded(translation: translation)
        }

        gestureView.onScrollWheel = { [weak self] deltaY, isCmdPressed in
            self?.handleScrollWheel(deltaY: deltaY, isCmdPressed: isCmdPressed)
        }

        // NSScrollView没有delegate属性，我们需要监听通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(scrollViewDidEndLiveMagnify(_:)),
            name: NSScrollView.didEndLiveMagnifyNotification,
            object: scrollView
        )

        // 添加视图
        addSubview(scrollView)
        addSubview(gestureView)

        setupConstraints()
        setupGestureRecognizers()
    }

    private func setupConstraints() {
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: bottomAnchor),

            // 手势视图覆盖整个区域
            gestureView.topAnchor.constraint(equalTo: topAnchor),
            gestureView.leadingAnchor.constraint(equalTo: leadingAnchor),
            gestureView.trailingAnchor.constraint(equalTo: trailingAnchor),
            gestureView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }

    private func setupGestureRecognizers() {
        // 添加双击手势识别器
        let doubleClickGesture = NSClickGestureRecognizer(target: self, action: #selector(handleDoubleClick(_:)))
        doubleClickGesture.numberOfClicksRequired = 2
        gestureView.addGestureRecognizer(doubleClickGesture)

        // 添加单击手势识别器
        let singleClickGesture = NSClickGestureRecognizer(target: self, action: #selector(handleSingleClick(_:)))
        singleClickGesture.numberOfClicksRequired = 1
        gestureView.addGestureRecognizer(singleClickGesture)

        // 添加放大手势识别器
        let magnificationGesture = NSMagnificationGestureRecognizer(target: self, action: #selector(handleMagnification(_:)))
        gestureView.addGestureRecognizer(magnificationGesture)
    }
    
    // MARK: - Public Methods

    func setImage(_ image: NSImage?) {
        currentImage = image
        imageView.image = image
        isLoading = false

        if let image = image {
            // 重置变换
            resetView()

            // 调整图片视图大小
            let imageSize = image.size
            imageView.frame = NSRect(origin: .zero, size: imageSize)

            // 居中显示
            centerImage()
        }
    }

    func setZoom(_ zoom: CGFloat) {
        let clampedZoom = max(minScale, min(maxScale, zoom))
        currentZoom = clampedZoom
        scrollView.magnification = clampedZoom
        delegate?.imagePreviewView(self, didChangeZoom: clampedZoom)
    }

    func rotateBy(_ angle: CGFloat) {
        currentRotation += angle
        // 保持旋转角度在 0-360 度范围内
        while currentRotation >= 360 {
            currentRotation -= 360
        }
        while currentRotation < 0 {
            currentRotation += 360
        }
        applyTransform()
        delegate?.imagePreviewView(self, didChangeRotation: currentRotation)
    }

    func resetView() {
        currentZoom = 1.0
        currentRotation = 0.0
        currentOffset = NSPoint.zero
        scrollView.magnification = 1.0
        applyTransform()
        centerImage()
        delegate?.imagePreviewView(self, didChangeZoom: currentZoom)
        delegate?.imagePreviewView(self, didChangeRotation: currentRotation)
    }

    func getCurrentZoom() -> CGFloat {
        return currentZoom
    }

    func getCurrentRotation() -> CGFloat {
        return currentRotation
    }
    
    // MARK: - Private Methods

    private func applyTransform() {
        guard let image = currentImage else { return }

        // 应用变换到图片视图
        imageView.frameRotation = currentRotation

        // 调整图片视图大小以适应旋转
        let rotatedSize = rotatedImageSize(image.size, rotation: currentRotation)
        imageView.frame = NSRect(origin: .zero, size: rotatedSize)

        centerImage()
    }

    private func rotatedImageSize(_ size: NSSize, rotation: CGFloat) -> NSSize {
        let radians = abs(rotation * .pi / 180)
        let sin = abs(sin(radians))
        let cos = abs(cos(radians))

        let width = size.width * cos + size.height * sin
        let height = size.width * sin + size.height * cos

        return NSSize(width: width, height: height)
    }

    private func centerImage() {
        guard let documentView = scrollView.documentView else { return }

        let scrollViewSize = scrollView.bounds.size
        let documentSize = documentView.frame.size

        let horizontalOffset = max(0, (documentSize.width - scrollViewSize.width) / 2)
        let verticalOffset = max(0, (documentSize.height - scrollViewSize.height) / 2)

        scrollView.contentView.scroll(to: NSPoint(x: horizontalOffset, y: verticalOffset))
    }

    // MARK: - Gesture Handling

    private func handleDragChanged(translation: NSSize) {
        guard !isDragging else { return }
        isDragging = true

        // 实时更新图片位置
        let currentScrollPoint = scrollView.contentView.bounds.origin
        let newPoint = NSPoint(
            x: currentScrollPoint.x - translation.width,
            y: currentScrollPoint.y + translation.height // 翻转Y轴
        )

        scrollView.contentView.scroll(to: newPoint)
    }

    private func handleDragEnded(translation: NSSize) {
        isDragging = false

        // 最终位置调整
        let currentScrollPoint = scrollView.contentView.bounds.origin
        let finalPoint = NSPoint(
            x: currentScrollPoint.x - translation.width,
            y: currentScrollPoint.y + translation.height
        )

        scrollView.contentView.scroll(to: finalPoint)
    }

    private func handleScrollWheel(deltaY: CGFloat, isCmdPressed: Bool) {
        guard !isDragging else { return }

        if isCmdPressed {
            // Cmd + 滚轮 = 上下拖动
            let currentScrollPoint = scrollView.contentView.bounds.origin
            let newPoint = NSPoint(
                x: currentScrollPoint.x,
                y: currentScrollPoint.y - deltaY * dragSpeed * 2
            )
            scrollView.contentView.scroll(to: newPoint)
        } else {
            // 普通滚轮 = 缩放
            let zoomFactor: CGFloat = 1.1
            let newScale: CGFloat
            if deltaY > 0 {
                newScale = min(maxScale, currentZoom * zoomFactor)
            } else {
                newScale = max(minScale, currentZoom / zoomFactor)
            }

            // 添加缩放阈值，避免微小缩放
            let scaleThreshold: CGFloat = 0.01
            if abs(newScale - currentZoom) > scaleThreshold {
                setZoom(newScale)
            }
        }
    }
    
    // MARK: - Gesture Handlers

    @objc private func handleDoubleClick(_ gesture: NSClickGestureRecognizer) {
        if currentZoom == 1.0 {
            // 放大到适合窗口大小
            setZoom(2.0)
        } else {
            // 重置到原始大小
            setZoom(1.0)
        }
    }

    @objc private func handleSingleClick(_ gesture: NSClickGestureRecognizer) {
        // 单击可以用于显示/隐藏控制面板等功能
        // 这里暂时不实现，留给外部控制
    }

    @objc private func handleMagnification(_ gesture: NSMagnificationGestureRecognizer) {
        guard !isDragging else { return }

        let newScale = currentZoom * (1.0 + gesture.magnification)
        let clampedScale = max(minScale, min(maxScale, newScale))

        if gesture.state == .changed {
            setZoom(clampedScale)
        } else if gesture.state == .ended {
            gesture.magnification = 0
        }
    }

    // MARK: - Notification Handlers

    @objc private func scrollViewDidEndLiveMagnify(_ notification: Notification) {
        guard let scrollView = notification.object as? NSScrollView,
              scrollView == self.scrollView else { return }

        currentZoom = scrollView.magnification
        delegate?.imagePreviewView(self, didChangeZoom: currentZoom)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - GestureHandlerView

class GestureHandlerView: NSView {
    var onDragChanged: ((NSSize) -> Void)?
    var onDragEnded: ((NSSize) -> Void)?
    var onScrollWheel: ((CGFloat, Bool) -> Void)?

    private var initialDragLocation: NSPoint?
    private var totalTranslation: NSSize = NSSize.zero
    private var lastDragUpdate: Date = Date()
    private let dragUpdateInterval: TimeInterval = 1.0 / 60.0 // 限制到60fps

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        // 确保视图可以接收鼠标事件
        wantsLayer = true
        layer?.backgroundColor = NSColor.clear.cgColor
    }

    override func mouseDown(with event: NSEvent) {
        // 转换为视图坐标
        let locationInView = convert(event.locationInWindow, from: nil)
        initialDragLocation = locationInView
        totalTranslation = NSSize.zero
        lastDragUpdate = Date()
        window?.makeFirstResponder(self)
    }

    override func mouseDragged(with event: NSEvent) {
        // 限制更新频率，避免过度更新导致抖动
        let now = Date()
        guard now.timeIntervalSince(lastDragUpdate) >= dragUpdateInterval else {
            return
        }
        lastDragUpdate = now

        guard let initialLocation = initialDragLocation else {
            return
        }
        // 转换为视图坐标
        let currentLocation = convert(event.locationInWindow, from: nil)

        // 计算偏移量，使用更精确的坐标系转换
        let deltaX = currentLocation.x - initialLocation.x
        let deltaY = -(currentLocation.y - initialLocation.y) // 翻转 Y 轴

        // 添加阈值，避免微小移动
        let threshold: CGFloat = 0.5
        if abs(deltaX) < threshold && abs(deltaY) < threshold {
            return
        }

        totalTranslation = NSSize(width: deltaX, height: deltaY)
        onDragChanged?(totalTranslation)
    }

    override func mouseUp(with event: NSEvent) {
        guard let initialLocation = initialDragLocation else {
            return
        }
        // 转换为视图坐标
        let currentLocation = convert(event.locationInWindow, from: nil)

        // 计算最终偏移量，使用更精确的坐标系转换
        let finalTranslation = NSSize(
            width: currentLocation.x - initialLocation.x,
            height: -(currentLocation.y - initialLocation.y) // 翻转 Y 轴
        )
        onDragEnded?(finalTranslation)
        initialDragLocation = nil
        totalTranslation = NSSize.zero
    }

    override func scrollWheel(with event: NSEvent) {
        // 处理滚轮事件，优先处理垂直滚动
        let deltaY = event.scrollingDeltaY
        let isCmdPressed = event.modifierFlags.contains(.command)

        if abs(deltaY) > 0.1 { // 添加阈值避免微小滚动
            onScrollWheel?(deltaY, isCmdPressed)
        }
    }

    override var acceptsFirstResponder: Bool {
        return true
    }

    override func becomeFirstResponder() -> Bool {
        return true
    }

    override func updateTrackingAreas() {
        super.updateTrackingAreas()

        // 移除旧的跟踪区域
        for trackingArea in trackingAreas {
            removeTrackingArea(trackingArea)
        }

        // 添加新的跟踪区域以接收鼠标事件
        let trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.activeInKeyWindow, .mouseEnteredAndExited, .mouseMoved],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea)
    }
}

//
//  SessionManager.swift
//  PhotoCC
//
//  Created by wtb on 2025/7/12.
//

import Foundation
import AppKit

// MARK: - SessionManager Notifications
extension Notification.Name {
    static let sessionsDidChange = Notification.Name("SessionsDidChange")
    static let currentSessionDidChange = Notification.Name("CurrentSessionDidChange")
}

// MARK: - SessionManager
class SessionManager {
    static let shared = SessionManager()

    private(set) var sessions: [Session] = [] {
        didSet {
            NotificationCenter.default.post(name: .sessionsDidChange, object: self)
        }
    }

    private(set) var currentSession: Session? {
        didSet {
            NotificationCenter.default.post(name: .currentSessionDidChange, object: self)
        }
    }
    
    private let userDefaults = UserDefaults.standard
    private let sessionsKey = "PhotoUncle_Sessions"
    private let currentSessionKey = "PhotoUncle_CurrentSession"
    
    private init() {
        loadSessions()
        loadCurrentSession()
    }
    
    // MARK: - Session Management
    
    /// 创建新会话
    func createSession(name: String, directoryPath: String, bookmarkData: Data? = nil) -> Session {
        let session = Session(name: name, directoryPath: directoryPath, bookmarkData: bookmarkData)

        // 检查是否已存在相同路径的会话
        if let existingIndex = sessions.firstIndex(where: { $0.directoryPath == directoryPath }) {
            // 更新现有会话
            let updatedSession = sessions[existingIndex].withUpdatedAccessTime().withBookmarkData(bookmarkData)
            var updatedSessions = sessions
            updatedSessions[existingIndex] = updatedSession
            sessions = updatedSessions
            currentSession = updatedSession
            saveSessions()
            return updatedSession
        } else {
            // 创建新会话
            var updatedSessions = sessions
            updatedSessions.insert(session, at: 0)

            // 限制会话数量为20个
            if updatedSessions.count > 20 {
                updatedSessions = Array(updatedSessions.prefix(20))
            }

            sessions = updatedSessions
            currentSession = session
            saveSessions()
            return session
        }
    }

    /// 打开会话
    func openSession(_ session: Session) {
        // 更新访问时间
        if let index = sessions.firstIndex(where: { $0.id == session.id }) {
            var updatedSessions = sessions
            updatedSessions[index] = session.withUpdatedAccessTime()
            sessions = updatedSessions
            currentSession = sessions[index]
            saveSessions()
        }
    }

    /// 移除会话
    func removeSession(_ session: Session) {
        var updatedSessions = sessions
        updatedSessions.removeAll { $0.id == session.id }
        sessions = updatedSessions

        // 如果移除的是当前会话，清空当前会话
        if currentSession?.id == session.id {
            currentSession = nil
        }

        saveSessions()
    }

    /// 清除所有会话
    func clearAllSessions() {
        sessions = []
        currentSession = nil
        saveSessions()
    }
    
    /// 获取最近的会话
    func getRecentSessions(limit: Int = 10) -> [Session] {
        return Array(sessions.prefix(limit))
    }
    
    // MARK: - Directory Selection
    
    /// 使用NSOpenPanel选择目录
    func selectDirectory() -> URL? {
        let openPanel = NSOpenPanel()
        openPanel.canChooseFiles = false
        openPanel.canChooseDirectories = true
        openPanel.allowsMultipleSelection = false
        openPanel.message = "选择一个包含图片的文件夹"
        openPanel.prompt = "选择"
        
        let response = openPanel.runModal()
        if response == .OK {
            return openPanel.url
        }
        return nil
    }
    
    /// 创建带bookmarkData的会话
    func createSessionWithBookmark(from url: URL) -> Session? {
        do {
            // 创建bookmarkData
            let bookmarkData = try url.bookmarkData(
                options: .withSecurityScope,
                includingResourceValuesForKeys: nil,
                relativeTo: nil
            )
            
            let directoryName = url.lastPathComponent
            let session = createSession(
                name: directoryName,
                directoryPath: url.path,
                bookmarkData: bookmarkData
            )
            
            print("✅ 已创建带bookmarkData的会话: \(session.name)")
            return session
            
        } catch {
            print("❌ 创建bookmarkData失败: \(error)")
            return nil
        }
    }
    
    /// 更新会话的bookmarkData
    func updateSessionBookmark(_ session: Session, with url: URL) -> Session? {
        do {
            // 创建新的bookmarkData
            let bookmarkData = try url.bookmarkData(
                options: .withSecurityScope,
                includingResourceValuesForKeys: nil,
                relativeTo: nil
            )

            let updatedSession = session.withBookmarkData(bookmarkData)

            // 更新会话列表
            if let index = sessions.firstIndex(where: { $0.id == session.id }) {
                var updatedSessions = sessions
                updatedSessions[index] = updatedSession
                sessions = updatedSessions

                if currentSession?.id == session.id {
                    currentSession = updatedSession
                }
                saveSessions()
            }

            print("✅ 已更新会话bookmarkData: \(updatedSession.name)")
            return updatedSession

        } catch {
            print("❌ 更新bookmarkData失败: \(error)")
            return nil
        }
    }
    
    // MARK: - Persistence
    
    private func saveSessions() {
        do {
            let data = try JSONEncoder().encode(sessions)
            userDefaults.set(data, forKey: sessionsKey)
            
            if let currentSession = currentSession {
                let currentData = try JSONEncoder().encode(currentSession)
                userDefaults.set(currentData, forKey: currentSessionKey)
            } else {
                userDefaults.removeObject(forKey: currentSessionKey)
            }
        } catch {
            print("❌ 保存会话失败: \(error)")
        }
    }
    
    private func loadSessions() {
        guard let data = userDefaults.data(forKey: sessionsKey) else { return }
        
        do {
            sessions = try JSONDecoder().decode([Session].self, from: data)
            print("✅ 已加载 \(sessions.count) 个会话")
        } catch {
            print("❌ 加载会话失败: \(error)")
            sessions = []
        }
    }
    
    private func loadCurrentSession() {
        guard let data = userDefaults.data(forKey: currentSessionKey) else { return }
        
        do {
            currentSession = try JSONDecoder().decode(Session.self, from: data)
            print("✅ 已加载当前会话: \(currentSession?.name ?? "未知")")
        } catch {
            print("❌ 加载当前会话失败: \(error)")
            currentSession = nil
        }
    }
}

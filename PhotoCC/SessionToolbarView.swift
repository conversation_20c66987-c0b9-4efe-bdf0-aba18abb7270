//
//  SessionToolbarView.swift
//  PhotoCC
//
//  Created by t<PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

protocol SessionToolbarViewDelegate: AnyObject {
    func sessionToolbarViewDidClickPrevious(_ toolbarView: SessionToolbarView)
    func sessionToolbarViewDidClickNext(_ toolbarView: SessionToolbarView)
    func sessionToolbarViewDidClickExport(_ toolbarView: SessionToolbarView)
    func sessionToolbarViewDidClickDelete(_ toolbarView: SessionToolbarView)
    func sessionToolbarViewDidClickInfo(_ toolbarView: SessionToolbarView)
}

class SessionToolbarView: NSView {
    
    // MARK: - Properties
    weak var delegate: SessionToolbarViewDelegate?
    
    private var backgroundView: NSVisualEffectView!
    private var previousButton: NSButton!
    private var nextButton: NSButton!
    private var exportButton: NSButton!
    private var deleteButton: NSButton!
    private var infoButton: NSButton!
    private var titleLabel: NSTextField!
    private var countLabel: NSTextField!
    
    // MARK: - Initialization
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        wantsLayer = true
        
        // 创建背景视图
        setupBackgroundView()
        
        // 创建控件
        setupControls()
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupBackgroundView() {
        backgroundView = NSVisualEffectView()
        backgroundView.material = .titlebar
        backgroundView.blendingMode = .behindWindow
        backgroundView.state = .active
        backgroundView.wantsLayer = true
        backgroundView.translatesAutoresizingMaskIntoConstraints = false
        
        addSubview(backgroundView)
    }
    
    private func setupControls() {
        // 上一张按钮
        previousButton = NSButton()
        previousButton.image = NSImage(systemSymbolName: "chevron.left", accessibilityDescription: "上一张")
        previousButton.bezelStyle = .regularSquare
        previousButton.isBordered = false
        previousButton.target = self
        previousButton.action = #selector(previousButtonClicked)
        previousButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 下一张按钮
        nextButton = NSButton()
        nextButton.image = NSImage(systemSymbolName: "chevron.right", accessibilityDescription: "下一张")
        nextButton.bezelStyle = .regularSquare
        nextButton.isBordered = false
        nextButton.target = self
        nextButton.action = #selector(nextButtonClicked)
        nextButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 导出按钮
        exportButton = NSButton()
        exportButton.image = NSImage(systemSymbolName: "square.and.arrow.up", accessibilityDescription: "导出")
        exportButton.bezelStyle = .regularSquare
        exportButton.isBordered = false
        exportButton.target = self
        exportButton.action = #selector(exportButtonClicked)
        exportButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 删除按钮
        deleteButton = NSButton()
        deleteButton.image = NSImage(systemSymbolName: "trash", accessibilityDescription: "删除")
        deleteButton.bezelStyle = .regularSquare
        deleteButton.isBordered = false
        deleteButton.target = self
        deleteButton.action = #selector(deleteButtonClicked)
        deleteButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 信息按钮
        infoButton = NSButton()
        infoButton.image = NSImage(systemSymbolName: "info.circle", accessibilityDescription: "信息")
        infoButton.bezelStyle = .regularSquare
        infoButton.isBordered = false
        infoButton.target = self
        infoButton.action = #selector(infoButtonClicked)
        infoButton.translatesAutoresizingMaskIntoConstraints = false
        
        // 标题标签
        titleLabel = NSTextField(labelWithString: "")
        titleLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 计数标签
        countLabel = NSTextField(labelWithString: "")
        countLabel.font = NSFont.systemFont(ofSize: 12)
        countLabel.textColor = NSColor.secondaryLabelColor
        countLabel.alignment = .center
        countLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加到背景视图
        backgroundView.addSubview(previousButton)
        backgroundView.addSubview(nextButton)
        backgroundView.addSubview(exportButton)
        backgroundView.addSubview(deleteButton)
        backgroundView.addSubview(infoButton)
        backgroundView.addSubview(titleLabel)
        backgroundView.addSubview(countLabel)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 背景视图约束
            backgroundView.topAnchor.constraint(equalTo: topAnchor),
            backgroundView.leadingAnchor.constraint(equalTo: leadingAnchor),
            backgroundView.trailingAnchor.constraint(equalTo: trailingAnchor),
            backgroundView.bottomAnchor.constraint(equalTo: bottomAnchor),
            
            // 上一张按钮约束
            previousButton.leadingAnchor.constraint(equalTo: backgroundView.leadingAnchor, constant: 16),
            previousButton.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            previousButton.widthAnchor.constraint(equalToConstant: 32),
            previousButton.heightAnchor.constraint(equalToConstant: 32),
            
            // 下一张按钮约束
            nextButton.leadingAnchor.constraint(equalTo: previousButton.trailingAnchor, constant: 8),
            nextButton.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            nextButton.widthAnchor.constraint(equalToConstant: 32),
            nextButton.heightAnchor.constraint(equalToConstant: 32),
            
            // 标题标签约束
            titleLabel.centerXAnchor.constraint(equalTo: backgroundView.centerXAnchor),
            titleLabel.topAnchor.constraint(equalTo: backgroundView.topAnchor, constant: 8),
            
            // 计数标签约束
            countLabel.centerXAnchor.constraint(equalTo: backgroundView.centerXAnchor),
            countLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 2),
            
            // 信息按钮约束
            infoButton.trailingAnchor.constraint(equalTo: backgroundView.trailingAnchor, constant: -16),
            infoButton.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            infoButton.widthAnchor.constraint(equalToConstant: 32),
            infoButton.heightAnchor.constraint(equalToConstant: 32),
            
            // 删除按钮约束
            deleteButton.trailingAnchor.constraint(equalTo: infoButton.leadingAnchor, constant: -8),
            deleteButton.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            deleteButton.widthAnchor.constraint(equalToConstant: 32),
            deleteButton.heightAnchor.constraint(equalToConstant: 32),
            
            // 导出按钮约束
            exportButton.trailingAnchor.constraint(equalTo: deleteButton.leadingAnchor, constant: -8),
            exportButton.centerYAnchor.constraint(equalTo: backgroundView.centerYAnchor),
            exportButton.widthAnchor.constraint(equalToConstant: 32),
            exportButton.heightAnchor.constraint(equalToConstant: 32)
        ])
    }
    
    // MARK: - Actions
    
    @objc private func previousButtonClicked() {
        delegate?.sessionToolbarViewDidClickPrevious(self)
    }
    
    @objc private func nextButtonClicked() {
        delegate?.sessionToolbarViewDidClickNext(self)
    }
    
    @objc private func exportButtonClicked() {
        delegate?.sessionToolbarViewDidClickExport(self)
    }
    
    @objc private func deleteButtonClicked() {
        delegate?.sessionToolbarViewDidClickDelete(self)
    }
    
    @objc private func infoButtonClicked() {
        delegate?.sessionToolbarViewDidClickInfo(self)
    }
    
    // MARK: - Public Methods
    
    func setTitle(_ title: String) {
        titleLabel.stringValue = title
    }
    
    func setCount(current: Int, total: Int) {
        countLabel.stringValue = "\(current) / \(total)"
    }
    
    func enableNavigationButtons(_ enable: Bool) {
        previousButton.isEnabled = enable
        nextButton.isEnabled = enable
    }
}

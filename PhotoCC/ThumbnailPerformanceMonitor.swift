//
//  ThumbnailPerformanceMonitor.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/13.
//

import SwiftUI
import Foundation

// MARK: - Performance Monitor

class ThumbnailPerformanceMonitor: ObservableObject {
    static let shared = ThumbnailPerformanceMonitor()
    
    @Published var isEnabled = false
    @Published var loadingCount = 0
    @Published var cacheHitRate: Double = 0.0
    @Published var averageLoadTime: Double = 0.0
    
    private var loadStartTimes: [URL: Date] = [:]
    private var totalLoadTime: Double = 0.0
    private var totalLoads = 0
    private var cacheHits = 0
    private var cacheMisses = 0
    private let queue = DispatchQueue(label: "performance.monitor", attributes: .concurrent)
    
    private init() {}
    
    func startLoad(for url: URL) {
        guard isEnabled else { return }
        queue.async(flags: .barrier) {
            self.loadStartTimes[url] = Date()
        }
        DispatchQueue.main.async {
            self.loadingCount += 1
        }
    }
    
    func endLoad(for url: URL, fromCache: Bool) {
        guard isEnabled else { return }
        
        queue.async(flags: .barrier) {
            if let startTime = self.loadStartTimes[url] {
                let loadTime = Date().timeIntervalSince(startTime)
                self.totalLoadTime += loadTime
                self.totalLoads += 1
                self.loadStartTimes.removeValue(forKey: url)
                
                DispatchQueue.main.async {
                    self.loadingCount = max(0, self.loadingCount - 1)
                    self.averageLoadTime = self.totalLoadTime / Double(self.totalLoads)
                }
            }
            
            if fromCache {
                self.cacheHits += 1
            } else {
                self.cacheMisses += 1
            }
            
            DispatchQueue.main.async {
                let total = self.cacheHits + self.cacheMisses
                self.cacheHitRate = total > 0 ? Double(self.cacheHits) / Double(total) : 0.0
            }
        }
    }
    
    func reset() {
        queue.async(flags: .barrier) {
            self.loadStartTimes.removeAll()
            self.totalLoadTime = 0.0
            self.totalLoads = 0
            self.cacheHits = 0
            self.cacheMisses = 0

            DispatchQueue.main.async {
                self.loadingCount = 0
                self.cacheHitRate = 0.0
                self.averageLoadTime = 0.0
            }
        }
    }

    func cleanup() {
        queue.async(flags: .barrier) {
            self.loadStartTimes.removeAll()
            DispatchQueue.main.async {
                self.loadingCount = 0
            }
        }
    }
}

// MARK: - Performance Monitor View

struct ThumbnailPerformanceView: View {
    @ObservedObject private var monitor = ThumbnailPerformanceMonitor.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("缩略图性能监控")
                    .font(.headline)
                Spacer()
                Toggle("启用", isOn: $monitor.isEnabled)
                    .toggleStyle(SwitchToggleStyle())
            }
            
            if monitor.isEnabled {
                Divider()
                
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("正在加载: \(monitor.loadingCount)")
                            .font(.caption)
                        Text("缓存命中率: \(String(format: "%.1f%%", monitor.cacheHitRate * 100))")
                            .font(.caption)
                        Text("平均加载时间: \(String(format: "%.2f ms", monitor.averageLoadTime * 1000))")
                            .font(.caption)
                    }
                    
                    Spacer()
                    
                    Button("重置") {
                        monitor.reset()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
                
                HStack {
                    Text("缓存大小: \(ThumbnailGenerator.shared.getCacheSize())")
                        .font(.caption)
                    
                    Spacer()
                    
                    Button("清理缓存") {
                        ThumbnailGenerator.shared.clearCache()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
        .onDisappear {
            // 清理监控器状态，避免内存泄漏
            monitor.cleanup()
        }
    }
}

#Preview {
    ThumbnailPerformanceView()
        .frame(width: 300)
}

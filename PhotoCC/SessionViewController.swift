//
//  SessionViewController.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

class SessionViewController: NSViewController {

    // MARK: - Properties
    private let session: Session
    private var photoLoader: PhotoLoader!

    // UI Components
    private var toolbarView: SessionToolbarView!
    private var previewContainerView: NSView!
    private var imagePreviewView: ImagePreviewView!
    private var controlPanel: PreviewControlPanel!
    private var thumbnailListView: ThumbnailListView!

    // State
    private var currentPhotoIndex: Int = -1
    private var photos: [PhotoItem] = []
    
    // MARK: - Initialization
    
    init(session: Session) {
        self.session = session
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func loadView() {
        view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor

        setupPhotoLoader()
        setupUI()
        setupBindings()
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        loadPhotos()
    }
    
    // MARK: - Setup

    private func setupPhotoLoader() {
        photoLoader = PhotoLoader()
    }

    private func setupUI() {
        // 创建工具栏
        setupToolbarView()

        // 创建预览容器
        setupPreviewContainerView()

        // 创建图片预览视图
        setupImagePreviewView()

        // 创建控制面板
        setupControlPanel()

        // 创建缩略图列表
        setupThumbnailListView()

        // 设置约束
        setupConstraints()
    }

    private func setupToolbarView() {
        toolbarView = SessionToolbarView()
        toolbarView.delegate = self
        toolbarView.translatesAutoresizingMaskIntoConstraints = false
        toolbarView.setTitle(session.name)

        view.addSubview(toolbarView)
    }

    private func setupPreviewContainerView() {
        previewContainerView = NSView()
        previewContainerView.wantsLayer = true
        previewContainerView.layer?.backgroundColor = NSColor.black.cgColor
        previewContainerView.translatesAutoresizingMaskIntoConstraints = false

        view.addSubview(previewContainerView)
    }

    private func setupImagePreviewView() {
        imagePreviewView = ImagePreviewView()
        imagePreviewView.delegate = self
        imagePreviewView.translatesAutoresizingMaskIntoConstraints = false

        previewContainerView.addSubview(imagePreviewView)
    }

    private func setupControlPanel() {
        controlPanel = PreviewControlPanel()
        controlPanel.delegate = self
        controlPanel.translatesAutoresizingMaskIntoConstraints = false

        previewContainerView.addSubview(controlPanel)
    }

    private func setupThumbnailListView() {
        thumbnailListView = ThumbnailListView()
        thumbnailListView.delegate = self
        thumbnailListView.translatesAutoresizingMaskIntoConstraints = false

        view.addSubview(thumbnailListView)
    }

    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 工具栏约束
            toolbarView.topAnchor.constraint(equalTo: view.topAnchor),
            toolbarView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            toolbarView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            toolbarView.heightAnchor.constraint(equalToConstant: 50),

            // 预览容器约束
            previewContainerView.topAnchor.constraint(equalTo: toolbarView.bottomAnchor),
            previewContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            previewContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),

            // 缩略图列表约束
            thumbnailListView.topAnchor.constraint(equalTo: previewContainerView.bottomAnchor),
            thumbnailListView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            thumbnailListView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            thumbnailListView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            thumbnailListView.heightAnchor.constraint(equalToConstant: 120),

            // 预览容器高度约束 (填充剩余空间)
            previewContainerView.bottomAnchor.constraint(equalTo: thumbnailListView.topAnchor),

            // 图片预览视图约束
            imagePreviewView.topAnchor.constraint(equalTo: previewContainerView.topAnchor),
            imagePreviewView.leadingAnchor.constraint(equalTo: previewContainerView.leadingAnchor),
            imagePreviewView.trailingAnchor.constraint(equalTo: previewContainerView.trailingAnchor),
            imagePreviewView.bottomAnchor.constraint(equalTo: previewContainerView.bottomAnchor),

            // 控制面板约束
            controlPanel.leadingAnchor.constraint(equalTo: previewContainerView.leadingAnchor, constant: 20),
            controlPanel.trailingAnchor.constraint(equalTo: previewContainerView.trailingAnchor, constant: -20),
            controlPanel.bottomAnchor.constraint(equalTo: previewContainerView.bottomAnchor, constant: -20),
            controlPanel.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    private func setupBindings() {
        // 监听照片加载状态
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(photoLoaderDidStartLoading),
            name: .photoLoaderDidStartLoading,
            object: photoLoader
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(photoLoaderDidFinishLoading),
            name: .photoLoaderDidFinishLoading,
            object: photoLoader
        )

        // 监听照片数据变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(photoLoaderPhotosDidChange),
            name: .photoLoaderPhotosDidChange,
            object: photoLoader
        )
    }

    @objc private func photoLoaderDidStartLoading() {
        DispatchQueue.main.async {
            // 可以在这里显示加载状态
        }
    }

    @objc private func photoLoaderDidFinishLoading() {
        DispatchQueue.main.async {
            // 加载完成后的处理
        }
    }

    @objc private func photoLoaderPhotosDidChange() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.photos = self.photoLoader.photos
            self.thumbnailListView.setPhotos(self.photos)
            self.updateToolbarInfo()

            // 如果有照片，默认选择第一张
            if !self.photos.isEmpty {
                self.selectPhoto(at: 0)
            }
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Data Loading

    private func loadPhotos() {
        guard let directoryURL = session.directoryURL else {
            // 显示错误状态
            return
        }

        photoLoader.loadPhotosFromDirectory(directoryURL)
    }

    // MARK: - Photo Navigation

    private func selectPhoto(at index: Int) {
        guard index >= 0 && index < photos.count else { return }

        currentPhotoIndex = index
        let photo = photos[index]

        // 更新预览视图
        loadFullImage(for: photo)

        // 更新缩略图选择
        thumbnailListView.selectPhoto(at: index)

        // 更新工具栏信息
        updateToolbarInfo()
    }

    private func loadFullImage(for photo: PhotoItem) {
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            let image = photo.image

            DispatchQueue.main.async {
                self?.imagePreviewView.setImage(image)
            }
        }
    }

    private func updateToolbarInfo() {
        if currentPhotoIndex >= 0 && currentPhotoIndex < photos.count {
            let photo = photos[currentPhotoIndex]
            toolbarView.setTitle(photo.name)
            toolbarView.setCount(current: currentPhotoIndex + 1, total: photos.count)
            toolbarView.enableNavigationButtons(photos.count > 1)
        } else {
            toolbarView.setTitle(session.name)
            toolbarView.setCount(current: 0, total: photos.count)
            toolbarView.enableNavigationButtons(false)
        }
    }

    private func navigateToPrevious() {
        if currentPhotoIndex > 0 {
            selectPhoto(at: currentPhotoIndex - 1)
        }
    }

    private func navigateToNext() {
        if currentPhotoIndex < photos.count - 1 {
            selectPhoto(at: currentPhotoIndex + 1)
        }
    }
}

// MARK: - SessionToolbarViewDelegate

extension SessionViewController: SessionToolbarViewDelegate {

    func sessionToolbarViewDidClickPrevious(_ toolbarView: SessionToolbarView) {
        navigateToPrevious()
    }

    func sessionToolbarViewDidClickNext(_ toolbarView: SessionToolbarView) {
        navigateToNext()
    }

    func sessionToolbarViewDidClickExport(_ toolbarView: SessionToolbarView) {
        // TODO: 实现导出功能
    }

    func sessionToolbarViewDidClickDelete(_ toolbarView: SessionToolbarView) {
        // TODO: 实现删除功能
    }

    func sessionToolbarViewDidClickInfo(_ toolbarView: SessionToolbarView) {
        // TODO: 实现信息显示功能
    }
}

// MARK: - ThumbnailListViewDelegate

extension SessionViewController: ThumbnailListViewDelegate {

    func thumbnailListView(_ listView: ThumbnailListView, didSelectPhoto photo: PhotoItem) {
        if let index = photos.firstIndex(where: { $0.id == photo.id }) {
            selectPhoto(at: index)
        }
    }
}

// MARK: - ImagePreviewViewDelegate

extension SessionViewController: ImagePreviewViewDelegate {

    func imagePreviewView(_ previewView: ImagePreviewView, didChangeZoom zoom: CGFloat) {
        controlPanel.setZoom(zoom)
    }

    func imagePreviewView(_ previewView: ImagePreviewView, didChangeRotation rotation: CGFloat) {
        // 可以在这里更新UI或保存旋转状态
        // 目前不需要特殊处理
    }
}

// MARK: - PreviewControlPanelDelegate

extension SessionViewController: PreviewControlPanelDelegate {

    func previewControlPanel(_ panel: PreviewControlPanel, didChangeZoom zoom: CGFloat) {
        imagePreviewView.setZoom(zoom)
    }

    func previewControlPanel(_ panel: PreviewControlPanel, didRotateBy angle: CGFloat) {
        imagePreviewView.rotateBy(angle)
    }

    func previewControlPanelDidResetView(_ panel: PreviewControlPanel) {
        imagePreviewView.resetView()
    }
}

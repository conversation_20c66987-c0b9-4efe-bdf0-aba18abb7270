//
//  SessionCellView.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import AppKit

class SessionCellView: NSTableCellView {
    
    // MARK: - Properties
    private var nameLabel: NSTextField!
    private var pathLabel: NSTextField!
    private var timeLabel: NSTextField!
    private var iconImageView: NSImageView!
    
    // MARK: - Initialization
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        wantsLayer = true
        
        // 创建图标
        iconImageView = NSImageView()
        iconImageView.image = NSImage(systemSymbolName: "folder", accessibilityDescription: nil)
        iconImageView.contentTintColor = NSColor.systemBlue
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建名称标签
        nameLabel = NSTextField()
        nameLabel.isEditable = false
        nameLabel.isBordered = false
        nameLabel.backgroundColor = NSColor.clear
        nameLabel.font = NSFont.systemFont(ofSize: 14, weight: .medium)
        nameLabel.textColor = NSColor.labelColor
        nameLabel.lineBreakMode = .byTruncatingTail
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建路径标签
        pathLabel = NSTextField()
        pathLabel.isEditable = false
        pathLabel.isBordered = false
        pathLabel.backgroundColor = NSColor.clear
        pathLabel.font = NSFont.systemFont(ofSize: 11)
        pathLabel.textColor = NSColor.secondaryLabelColor
        pathLabel.lineBreakMode = .byTruncatingMiddle
        pathLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建时间标签
        timeLabel = NSTextField()
        timeLabel.isEditable = false
        timeLabel.isBordered = false
        timeLabel.backgroundColor = NSColor.clear
        timeLabel.font = NSFont.systemFont(ofSize: 10)
        timeLabel.textColor = NSColor.tertiaryLabelColor
        timeLabel.alignment = .right
        timeLabel.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加子视图
        addSubview(iconImageView)
        addSubview(nameLabel)
        addSubview(pathLabel)
        addSubview(timeLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 图标约束
            iconImageView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 12),
            iconImageView.centerYAnchor.constraint(equalTo: centerYAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 20),
            iconImageView.heightAnchor.constraint(equalToConstant: 20),
            
            // 名称标签约束
            nameLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 8),
            nameLabel.topAnchor.constraint(equalTo: topAnchor, constant: 8),
            nameLabel.trailingAnchor.constraint(equalTo: timeLabel.leadingAnchor, constant: -8),
            
            // 路径标签约束
            pathLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            pathLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 2),
            pathLabel.trailingAnchor.constraint(equalTo: nameLabel.trailingAnchor),
            
            // 时间标签约束
            timeLabel.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -12),
            timeLabel.topAnchor.constraint(equalTo: topAnchor, constant: 8),
            timeLabel.widthAnchor.constraint(equalToConstant: 80)
        ])
    }
    
    // MARK: - Configuration
    
    func configure(with session: Session) {
        nameLabel.stringValue = session.name
        pathLabel.stringValue = session.directoryPath
        
        // 格式化时间
        let formatter = RelativeDateTimeFormatter()
        formatter.dateTimeStyle = .named
        timeLabel.stringValue = formatter.localizedString(for: session.lastAccessedAt, relativeTo: Date())
        
        // 根据会话状态设置图标颜色
        if session.isDirectoryValid {
            iconImageView.contentTintColor = NSColor.systemBlue
        } else {
            iconImageView.contentTintColor = NSColor.systemRed
        }
    }
    
    // MARK: - Selection Handling
    
    override var backgroundStyle: NSView.BackgroundStyle {
        didSet {
            updateAppearance()
        }
    }
    
    private func updateAppearance() {
        switch backgroundStyle {
        case .emphasized:
            // 选中状态
            nameLabel.textColor = NSColor.alternateSelectedControlTextColor
            pathLabel.textColor = NSColor.alternateSelectedControlTextColor.withAlphaComponent(0.8)
            timeLabel.textColor = NSColor.alternateSelectedControlTextColor.withAlphaComponent(0.6)
        default:
            // 正常状态
            nameLabel.textColor = NSColor.labelColor
            pathLabel.textColor = NSColor.secondaryLabelColor
            timeLabel.textColor = NSColor.tertiaryLabelColor
        }
    }
}

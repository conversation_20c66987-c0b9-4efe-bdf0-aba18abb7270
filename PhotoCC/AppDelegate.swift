//
//  AppDelegate.swift
//  PhotoCC
//
//  Created by <PERSON><PERSON><PERSON> wang on 2025/7/15.
//

import Cocoa

@main
class AppDelegate: NSObject, NSApplicationDelegate {

    @IBOutlet var window: NSWindow!
    private var mainWindowController: MainWindowController?

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // 隐藏XIB窗口
        if window != nil {
            window?.orderOut(nil)
            window?.close()
        }

        // 创建主窗口控制器
        mainWindowController = MainWindowController()
        mainWindowController?.showWindow(nil)
    }

    func applicationWillTerminate(_ aNotification: Notification) {
        // Insert code here to tear down your application
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}


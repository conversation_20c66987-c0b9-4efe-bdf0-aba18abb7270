//
//  PhotoLoader.swift
//  PhotoCC
//
//  Created by wtb on 2025/7/12.
//

import Foundation

// MARK: - PhotoLoader Notifications
extension Notification.Name {
    static let photoLoaderDidStartLoading = Notification.Name("PhotoLoaderDidStartLoading")
    static let photoLoaderDidFinishLoading = Notification.Name("PhotoLoaderDidFinishLoading")
    static let photoLoaderPhotosDidChange = Notification.Name("PhotoLoaderPhotosDidChange")
}

// MARK: - PhotoLoader
class PhotoLoader {
    private(set) var photos: [PhotoItem] = [] {
        didSet {
            NotificationCenter.default.post(name: .photoLoaderPhotosDidChange, object: self)
        }
    }

    private(set) var loadedDirectories: [URL] = []

    private(set) var isLoading: Bool = false {
        didSet {
            if isLoading {
                NotificationCenter.default.post(name: .photoLoaderDidStartLoading, object: self)
            } else {
                NotificationCenter.default.post(name: .photoLoaderDidFinishLoading, object: self)
            }
        }
    }

    private let supportedImageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "tif", "heic", "webp"]

    func loadPhotosFromDirectory(_ url: URL) {
        loadPhotosFromDirectories([url])
    }

    func loadPhotosFromDirectories(_ urls: [URL]) {
        isLoading = true
        photos.removeAll()
        loadedDirectories = urls

        // 清理缩略图缓存
        ThumbnailGenerator.shared.clearCache()

        var allPhotos: [PhotoItem] = []

        for url in urls {
            guard url.startAccessingSecurityScopedResource() else {
                print("无法访问目录: \(url)")
                continue
            }

            defer {
                url.stopAccessingSecurityScopedResource()
            }

            do {
                let fileURLs = try FileManager.default.contentsOfDirectory(
                    at: url,
                    includingPropertiesForKeys: [.isRegularFileKey, .contentTypeKey],
                    options: [.skipsHiddenFiles]
                )

                let imageURLs = fileURLs.filter { fileUrl in
                    let pathExtension = fileUrl.pathExtension.lowercased()
                    return supportedImageTypes.contains(pathExtension)
                }

                let photoItems = imageURLs.map { PhotoItem(url: $0) }
                allPhotos.append(contentsOf: photoItems)

            } catch {
                print("读取目录失败: \(error)")
            }
        }

        // 按文件名排序
        photos = allPhotos.sorted { $0.name < $1.name }
        isLoading = false
    }

    func addDirectory(_ url: URL) {
        var newDirectories = loadedDirectories
        if !newDirectories.contains(url) {
            newDirectories.append(url)
            loadPhotosFromDirectories(newDirectories)
        }
    }

    func removeDirectory(_ url: URL) {
        let newDirectories = loadedDirectories.filter { $0 != url }
        loadPhotosFromDirectories(newDirectories)
    }

    func refreshDirectories() {
        loadPhotosFromDirectories(loadedDirectories)
    }

    func clearAll() {
        photos.removeAll()
        loadedDirectories.removeAll()
        ThumbnailGenerator.shared.clearCache()
    }
}
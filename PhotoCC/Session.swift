//
//  Session.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import Foundation

// MARK: - Session Model
struct Session: Identifiable, Codable, Hashable {
    let id: UUID
    let name: String
    let directoryPath: String
    let bookmarkData: Data?
    let createdAt: Date
    let lastAccessedAt: Date
    
    init(name: String, directoryPath: String, bookmarkData: Data? = nil) {
        self.id = UUID()
        self.name = name
        self.directoryPath = directoryPath
        self.bookmarkData = bookmarkData
        self.createdAt = Date()
        self.lastAccessedAt = Date()
    }
    
    init(id: UUID, name: String, directoryPath: String, bookmarkData: Data?, createdAt: Date, lastAccessedAt: Date) {
        self.id = id
        self.name = name
        self.directoryPath = directoryPath
        self.bookmarkData = bookmarkData
        self.createdAt = createdAt
        self.lastAccessedAt = lastAccessedAt
    }
    
    // 更新最后访问时间
    func withUpdatedAccessTime() -> Session {
        return Session(
            id: self.id,
            name: self.name,
            directoryPath: self.directoryPath,
            bookmarkData: self.bookmarkData,
            createdAt: self.createdAt,
            lastAccessedAt: Date()
        )
    }
    
    // 更新bookmarkData
    func withBookmarkData(_ bookmarkData: Data?) -> Session {
        return Session(
            id: self.id,
            name: self.name,
            directoryPath: self.directoryPath,
            bookmarkData: bookmarkData,
            createdAt: self.createdAt,
            lastAccessedAt: self.lastAccessedAt
        )
    }
    
    // 获取目录URL（使用bookmarkData恢复访问权限）
    var directoryURL: URL? {
        guard let bookmarkData = bookmarkData else {
            // 如果没有bookmarkData，返回普通URL（可能无法访问）
            return URL(fileURLWithPath: directoryPath)
        }
        
        var isStale = false
        do {
            let url = try URL(resolvingBookmarkData: bookmarkData, 
                            options: .withSecurityScope, 
                            relativeTo: nil, 
                            bookmarkDataIsStale: &isStale)
            return url
        } catch {
            print("❌ 无法解析bookmarkData: \(error)")
            return nil
        }
    }
    
    // 检查目录是否仍然存在且可访问
    var isDirectoryValid: Bool {
        guard let url = directoryURL else { return false }
        
        // 尝试获取安全作用域访问权限
        guard url.startAccessingSecurityScopedResource() else {
            return false
        }
        
        defer {
            url.stopAccessingSecurityScopedResource()
        }
        
        var isDirectory: ObjCBool = false
        let exists = FileManager.default.fileExists(atPath: url.path, isDirectory: &isDirectory)
        return exists && isDirectory.boolValue
    }
    
    // 检查是否有有效的bookmarkData
    var hasValidBookmarkData: Bool {
        return bookmarkData != nil && directoryURL != nil
    }
    
    // 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    // 格式化的最后访问时间
    var formattedLastAccessedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: lastAccessedAt)
    }
}
